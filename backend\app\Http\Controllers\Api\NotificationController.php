<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * عرض قائمة الإشعارات
     */
    public function index(Request $request): JsonResponse
    {
        $query = Notification::query()
                           ->where('company_id', auth()->user()->company_id)
                           ->orderBy('created_at', 'desc');

        // فلترة حسب الحالة
        if ($request->has('status')) {
            if ($request->status === 'unread') {
                $query->unread();
            } elseif ($request->status === 'read') {
                $query->read();
            }
        }

        // فلترة حسب النوع
        if ($request->has('type')) {
            $query->byType($request->type);
        }

        // فلترة حسب الأولوية
        if ($request->has('priority')) {
            $query->byPriority($request->priority);
        }

        // فلترة حسب التاريخ
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $perPage = $request->get('per_page', 15);
        $notifications = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $notifications
        ]);
    }

    /**
     * عرض تفاصيل إشعار محدد
     */
    public function show($id): JsonResponse
    {
        $notification = Notification::where('company_id', auth()->user()->company_id)
                                  ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $notification
        ]);
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead($id): JsonResponse
    {
        $notification = Notification::where('company_id', auth()->user()->company_id)
                                  ->findOrFail($id);

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء'
        ]);
    }

    /**
     * تحديد إشعار كغير مقروء
     */
    public function markAsUnread($id): JsonResponse
    {
        $notification = Notification::where('company_id', auth()->user()->company_id)
                                  ->findOrFail($id);

        $notification->markAsUnread();

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كغير مقروء'
        ]);
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead(): JsonResponse
    {
        $count = Notification::where('company_id', auth()->user()->company_id)
                           ->unread()
                           ->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => "تم تحديد {$count} إشعار كمقروء",
            'count' => $count
        ]);
    }

    /**
     * حذف إشعار
     */
    public function destroy($id): JsonResponse
    {
        $notification = Notification::where('company_id', auth()->user()->company_id)
                                  ->findOrFail($id);

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الإشعار بنجاح'
        ]);
    }

    /**
     * حذف جميع الإشعارات المقروءة
     */
    public function deleteRead(): JsonResponse
    {
        $count = Notification::where('company_id', auth()->user()->company_id)
                           ->read()
                           ->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$count} إشعار مقروء",
            'count' => $count
        ]);
    }

    /**
     * إحصائيات الإشعارات
     */
    public function stats(): JsonResponse
    {
        $companyId = auth()->user()->company_id;
        $stats = Notification::getStats($companyId);

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * الحصول على الإشعارات غير المقروءة
     */
    public function unread(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        
        $notifications = Notification::where('company_id', auth()->user()->company_id)
                                   ->unread()
                                   ->orderBy('created_at', 'desc')
                                   ->limit($limit)
                                   ->get();

        return response()->json([
            'success' => true,
            'data' => $notifications,
            'count' => $notifications->count()
        ]);
    }

    /**
     * إنشاء إشعار جديد (للمشرفين)
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|max:50',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'priority' => 'in:low,normal,high,urgent',
            'channel' => 'in:database,email,sms,push',
            'data' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $notification = Notification::create([
            'type' => $request->type,
            'notifiable_type' => auth()->user()::class,
            'notifiable_id' => auth()->id(),
            'title' => $request->title,
            'title_en' => $request->title_en ?? $request->title,
            'message' => $request->message,
            'message_en' => $request->message_en ?? $request->message,
            'data' => $request->data ?? [],
            'priority' => $request->priority ?? 'normal',
            'channel' => $request->channel ?? 'database',
            'company_id' => auth()->user()->company_id,
            'branch_id' => auth()->user()->branch_id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الإشعار بنجاح',
            'data' => $notification
        ], 201);
    }

    /**
     * الحصول على أنواع الإشعارات
     */
    public function types(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => Notification::getTypes()
        ]);
    }

    /**
     * الحصول على مستويات الأولوية
     */
    public function priorities(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => Notification::getPriorities()
        ]);
    }

    /**
     * الحصول على قنوات الإرسال
     */
    public function channels(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => Notification::getChannels()
        ]);
    }

    /**
     * حذف الإشعارات القديمة
     */
    public function cleanup(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $count = Notification::cleanup($days);

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$count} إشعار قديم",
            'count' => $count
        ]);
    }
}
