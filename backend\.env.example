APP_NAME="Wisaq POS System"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

APP_LOCALE=ar
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=ar_SA

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wisaq_pos
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=null
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Tap Payment Gateway
TAP_SECRET_KEY=sk_test_your_secret_key
TAP_PUBLIC_KEY=pk_test_your_public_key
TAP_WEBHOOK_SECRET=your_webhook_secret
TAP_ENVIRONMENT=sandbox

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_TTL=60
JWT_REFRESH_TTL=20160

# File Upload Settings
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,csv

# System Settings
DEFAULT_CURRENCY=SAR
DEFAULT_TIMEZONE=Asia/Riyadh
VAT_RATE=15
COMPANY_LOGO_PATH=storage/logos
PRODUCT_IMAGE_PATH=storage/products

VITE_APP_NAME="${APP_NAME}"

# Tap Payment Gateway
TAP_SECRET_KEY=
TAP_PUBLIC_KEY=
TAP_WEBHOOK_SECRET=
TAP_ENVIRONMENT=sandbox

# Security Settings
SECURITY_ALERT_EMAIL=<EMAIL>
SECURITY_ALERT_WEBHOOK=

# JWT Settings
JWT_SECRET=
JWT_TTL=1440

# Rate Limiting
RATE_LIMIT_API=100
RATE_LIMIT_LOGIN=5
RATE_LIMIT_SENSITIVE=10

# File Upload
MAX_UPLOAD_SIZE=10240
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_DOCUMENT_EXTENSIONS=pdf,doc,docx,xls,xlsx,csv

# Backup Settings
BACKUP_ENCRYPT=true
BACKUP_RETENTION_DAYS=30

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Database Encryption
DB_ENCRYPT_SENSITIVE_DATA=true

# Subscription Settings
TRIAL_PERIOD_DAYS=14
DEFAULT_SUBSCRIPTION_PLAN=basic
BRANCH_ADDITIONAL_COST_PERCENTAGE=30
VITE_API_URL="${APP_URL}/api/v1"
