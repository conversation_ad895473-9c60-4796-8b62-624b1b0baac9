<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;
use App\Models\Branch;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Subscription;
use App\Models\Setting;
use App\Models\Permission;
use App\Models\Role;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات الأساسية
        $this->createPermissions();
        
        // إنشاء الأدوار الأساسية
        $this->createRoles();
        
        // إنشاء شركة تجريبية
        $company = $this->createDemoCompany();
        
        // إنشاء فرع تجريبي
        $branch = $this->createDemoBranch($company);
        
        // إنشاء مستخدمين تجريبيين
        $this->createDemoUsers($company, $branch);
        
        // إنشاء فئات المنتجات
        $categories = $this->createDemoCategories($company);
        
        // إنشاء منتجات تجريبية
        $this->createDemoProducts($company, $categories);
        
        // إنشاء عملاء تجريبيين
        $this->createDemoCustomers($company);
        
        // إنشاء موردين تجريبيين
        $this->createDemoSuppliers($company);
        
        // إنشاء اشتراك تجريبي
        $this->createDemoSubscription($company);
        
        // إنشاء إعدادات افتراضية
        $this->createDefaultSettings($company);
    }

    private function createPermissions(): void
    {
        $permissions = Permission::getDefaultPermissions();
        
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }
    }

    private function createRoles(): void
    {
        $roles = [
            [
                'name' => 'مدير النظام',
                'name_en' => 'System Admin',
                'slug' => 'admin',
                'description' => 'مدير النظام الرئيسي',
                'permissions' => Permission::pluck('slug')->toArray(),
                'is_system' => true
            ],
            [
                'name' => 'مالك الشركة',
                'name_en' => 'Company Owner',
                'slug' => 'owner',
                'description' => 'مالك الشركة',
                'permissions' => [
                    'view_dashboard', 'view_statistics', 'view_sales', 'create_sale',
                    'edit_sales', 'view_products', 'create_product', 'edit_products',
                    'view_inventory', 'edit_inventory', 'view_customers', 'create_customer',
                    'edit_customers', 'view_suppliers', 'create_supplier', 'edit_suppliers',
                    'view_reports', 'create_report', 'export_reports', 'view_users',
                    'create_user', 'edit_users', 'view_settings', 'edit_settings'
                ],
                'is_system' => true
            ],
            [
                'name' => 'مدير',
                'name_en' => 'Manager',
                'slug' => 'manager',
                'description' => 'مدير الفرع',
                'permissions' => [
                    'view_dashboard', 'view_statistics', 'view_sales', 'create_sale',
                    'edit_sales', 'view_products', 'create_product', 'edit_products',
                    'view_inventory', 'edit_inventory', 'view_customers', 'create_customer',
                    'edit_customers', 'view_suppliers', 'view_reports', 'export_reports'
                ],
                'is_system' => true
            ],
            [
                'name' => 'كاشير',
                'name_en' => 'Cashier',
                'slug' => 'cashier',
                'description' => 'موظف الكاشير',
                'permissions' => [
                    'view_dashboard', 'view_sales', 'create_sale', 'view_products',
                    'view_customers', 'create_customer'
                ],
                'is_system' => true
            ]
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['slug' => $roleData['slug']],
                $roleData
            );
        }
    }

    private function createDemoCompany(): Company
    {
        return Company::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'شركة ويساق التجريبية',
                'name_en' => 'Wisaq Demo Company',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'address' => 'الرياض، المملكة العربية السعودية',
                'city' => 'الرياض',
                'country' => 'SA',
                'tax_number' => '123456789012345',
                'commercial_register' => '1010123456',
                'subscription_plan' => 'basic',
                'subscription_status' => 'active',
                'subscription_expires_at' => now()->addMonth(),
                'trial_ends_at' => now()->addDays(14),
                'max_branches' => 5,
                'max_users' => 10,
                'is_active' => true
            ]
        );
    }

    private function createDemoBranch(Company $company): Branch
    {
        return Branch::firstOrCreate(
            [
                'company_id' => $company->id,
                'name' => 'الفرع الرئيسي'
            ],
            [
                'name_en' => 'Main Branch',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'phone' => '+966501234567',
                'email' => '<EMAIL>',
                'is_active' => true,
                'is_main' => true
            ]
        );
    }

    private function createDemoUsers(Company $company, Branch $branch): void
    {
        $users = [
            [
                'name' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'is_active' => true
            ],
            [
                'name' => 'سارة أحمد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'owner',
                'is_active' => true
            ],
            [
                'name' => 'محمد علي',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'is_active' => true
            ],
            [
                'name' => 'فاطمة خالد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'cashier',
                'is_active' => true
            ]
        ];

        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                array_merge($userData, [
                    'company_id' => $company->id,
                    'branch_id' => $branch->id,
                    'email_verified_at' => now()
                ])
            );

            // ربط المستخدم بالدور المناسب
            $role = Role::where('slug', $userData['role'])->first();
            if ($role && !$user->roles()->where('role_id', $role->id)->exists()) {
                $user->assignRole($role, $company->id, $branch->id);
            }
        }
    }

    private function createDemoCategories(Company $company): array
    {
        $categories = [
            [
                'name' => 'مشروبات',
                'name_en' => 'Beverages',
                'description' => 'جميع أنواع المشروبات',
                'color' => '#3B82F6',
                'icon' => 'coffee'
            ],
            [
                'name' => 'وجبات خفيفة',
                'name_en' => 'Snacks',
                'description' => 'الوجبات الخفيفة والحلويات',
                'color' => '#EF4444',
                'icon' => 'cookie'
            ],
            [
                'name' => 'منتجات الألبان',
                'name_en' => 'Dairy Products',
                'description' => 'الحليب والأجبان واللبن',
                'color' => '#10B981',
                'icon' => 'milk'
            ],
            [
                'name' => 'الخضروات والفواكه',
                'name_en' => 'Fruits & Vegetables',
                'description' => 'الخضروات والفواكه الطازجة',
                'color' => '#F59E0B',
                'icon' => 'apple'
            ]
        ];

        $createdCategories = [];
        foreach ($categories as $index => $categoryData) {
            $category = Category::firstOrCreate(
                [
                    'company_id' => $company->id,
                    'name' => $categoryData['name']
                ],
                array_merge($categoryData, [
                    'sort_order' => $index + 1,
                    'is_active' => true
                ])
            );
            $createdCategories[] = $category;
        }

        return $createdCategories;
    }

    private function createDemoProducts(Company $company, array $categories): void
    {
        $products = [
            // مشروبات
            [
                'name' => 'كوكا كولا',
                'name_en' => 'Coca Cola',
                'sku' => 'BEV001',
                'barcode' => '1234567890123',
                'cost_price' => 1.50,
                'selling_price' => 2.50,
                'stock_quantity' => 100,
                'min_stock_level' => 20,
                'unit' => 'علبة',
                'category_index' => 0
            ],
            [
                'name' => 'ماء معدني',
                'name_en' => 'Mineral Water',
                'sku' => 'BEV002',
                'barcode' => '1234567890124',
                'cost_price' => 0.50,
                'selling_price' => 1.00,
                'stock_quantity' => 200,
                'min_stock_level' => 50,
                'unit' => 'زجاجة',
                'category_index' => 0
            ],
            // وجبات خفيفة
            [
                'name' => 'شيبس ليز',
                'name_en' => 'Lays Chips',
                'sku' => 'SNK001',
                'barcode' => '1234567890125',
                'cost_price' => 2.00,
                'selling_price' => 3.50,
                'stock_quantity' => 75,
                'min_stock_level' => 15,
                'unit' => 'كيس',
                'category_index' => 1
            ],
            [
                'name' => 'شوكولاتة كيت كات',
                'name_en' => 'Kit Kat Chocolate',
                'sku' => 'SNK002',
                'barcode' => '1234567890126',
                'cost_price' => 1.75,
                'selling_price' => 3.00,
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'unit' => 'قطعة',
                'category_index' => 1
            ],
            // منتجات الألبان
            [
                'name' => 'حليب نادك',
                'name_en' => 'Nadec Milk',
                'sku' => 'DAI001',
                'barcode' => '1234567890127',
                'cost_price' => 3.00,
                'selling_price' => 4.50,
                'stock_quantity' => 30,
                'min_stock_level' => 5,
                'unit' => 'لتر',
                'category_index' => 2
            ],
            [
                'name' => 'جبنة كرافت',
                'name_en' => 'Kraft Cheese',
                'sku' => 'DAI002',
                'barcode' => '1234567890128',
                'cost_price' => 8.00,
                'selling_price' => 12.00,
                'stock_quantity' => 25,
                'min_stock_level' => 5,
                'unit' => 'علبة',
                'category_index' => 2
            ]
        ];

        foreach ($products as $productData) {
            $categoryIndex = $productData['category_index'];
            unset($productData['category_index']);

            Product::firstOrCreate(
                [
                    'company_id' => $company->id,
                    'sku' => $productData['sku']
                ],
                array_merge($productData, [
                    'category_id' => $categories[$categoryIndex]->id,
                    'track_stock' => true,
                    'is_active' => true
                ])
            );
        }
    }

    private function createDemoCustomers(Company $company): void
    {
        $customers = [
            [
                'customer_code' => 'CUS000001',
                'name' => 'عبدالله السعد',
                'email' => '<EMAIL>',
                'phone' => '+966501111111',
                'type' => 'individual',
                'category' => 'regular'
            ],
            [
                'customer_code' => 'CUS000002',
                'name' => 'شركة الأعمال المتقدمة',
                'email' => '<EMAIL>',
                'phone' => '+966502222222',
                'type' => 'company',
                'category' => 'wholesale',
                'credit_limit' => 10000.00
            ],
            [
                'customer_code' => 'CUS000003',
                'name' => 'نورا أحمد',
                'email' => '<EMAIL>',
                'phone' => '+966503333333',
                'type' => 'individual',
                'category' => 'vip'
            ]
        ];

        foreach ($customers as $customerData) {
            Customer::firstOrCreate(
                [
                    'company_id' => $company->id,
                    'customer_code' => $customerData['customer_code']
                ],
                array_merge($customerData, [
                    'country' => 'SA',
                    'is_active' => true
                ])
            );
        }
    }

    private function createDemoSuppliers(Company $company): void
    {
        $suppliers = [
            [
                'supplier_code' => 'SUP000001',
                'name' => 'شركة المشروبات الوطنية',
                'name_en' => 'National Beverages Company',
                'contact_person' => 'أحمد محمد',
                'email' => '<EMAIL>',
                'phone' => '+966504444444',
                'type' => 'local',
                'category' => 'distributor'
            ],
            [
                'supplier_code' => 'SUP000002',
                'name' => 'مصنع الحلويات الذهبية',
                'name_en' => 'Golden Sweets Factory',
                'contact_person' => 'سارة علي',
                'email' => '<EMAIL>',
                'phone' => '+966505555555',
                'type' => 'local',
                'category' => 'manufacturer'
            ]
        ];

        foreach ($suppliers as $supplierData) {
            Supplier::firstOrCreate(
                [
                    'company_id' => $company->id,
                    'supplier_code' => $supplierData['supplier_code']
                ],
                array_merge($supplierData, [
                    'country' => 'SA',
                    'is_active' => true
                ])
            );
        }
    }

    private function createDemoSubscription(Company $company): void
    {
        Subscription::firstOrCreate(
            ['company_id' => $company->id],
            [
                'plan_name' => 'الخطة الأساسية',
                'plan_type' => 'basic',
                'price' => 99.00,
                'currency' => 'SAR',
                'billing_cycle' => 'monthly',
                'max_branches' => 5,
                'max_users' => 10,
                'features' => [
                    'pos_system',
                    'inventory_management',
                    'basic_reports',
                    'customer_management',
                    'supplier_management'
                ],
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addMonth(),
                'trial_ends_at' => now()->addDays(14)
            ]
        );
    }

    private function createDefaultSettings(Company $company): void
    {
        $settings = Setting::getDefaultSettings();
        
        foreach ($settings as $setting) {
            Setting::firstOrCreate(
                [
                    'key' => $setting['key'],
                    'company_id' => $company->id
                ],
                array_merge($setting, [
                    'is_public' => true,
                    'is_editable' => true
                ])
            );
        }
    }
}
