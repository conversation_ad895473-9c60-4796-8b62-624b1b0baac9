<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Carbon\Carbon;

class Subscription extends Model
{
    protected $fillable = [
        'company_id',
        'plan_name',
        'plan_type',
        'price',
        'currency',
        'billing_cycle',
        'max_branches',
        'max_users',
        'features',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'metadata'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'metadata' => 'array',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    // الدوال المساعدة
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               $this->ends_at > now() &&
               !$this->cancelled_at;
    }

    public function isOnTrial(): bool
    {
        return $this->status === 'trial' &&
               $this->trial_ends_at > now();
    }

    public function isExpired(): bool
    {
        return $this->ends_at < now();
    }

    public function isCancelled(): bool
    {
        return !is_null($this->cancelled_at);
    }

    public function getDaysRemaining(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->ends_at);
    }

    public function getTrialDaysRemaining(): int
    {
        if (!$this->isOnTrial()) {
            return 0;
        }

        return now()->diffInDays($this->trial_ends_at);
    }

    public function markAsPaid(): void
    {
        $this->update([
            'status' => 'active',
            'starts_at' => now(),
        ]);
    }

    public function extendSubscription(): void
    {
        $endDate = $this->ends_at ?? now();

        $newEndDate = match($this->billing_cycle) {
            'monthly' => $endDate->addMonth(),
            'quarterly' => $endDate->addMonths(3),
            'yearly' => $endDate->addYear(),
            default => $endDate->addMonth()
        };

        $this->update(['ends_at' => $newEndDate]);
    }

    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], [
                'cancellation_reason' => $reason,
                'cancelled_by' => auth()->id()
            ])
        ]);
    }

    public function renew(): void
    {
        $this->update([
            'status' => 'active',
            'cancelled_at' => null,
            'ends_at' => $this->calculateNextBillingDate()
        ]);
    }

    private function calculateNextBillingDate(): Carbon
    {
        return match($this->billing_cycle) {
            'monthly' => now()->addMonth(),
            'quarterly' => now()->addMonths(3),
            'yearly' => now()->addYear(),
            default => now()->addMonth()
        };
    }

    public function calculateBranchPrice(): float
    {
        $currentBranches = $this->company->branches_count ?? 1;
        $baseBranches = 1; // الفرع الأول مجاني مع الاشتراك

        if ($currentBranches <= $baseBranches) {
            return $this->price;
        }

        $additionalBranches = $currentBranches - $baseBranches;
        $additionalSets = ceil($additionalBranches / 5); // كل 5 فروع إضافية
        $additionalCost = $this->price * 0.30 * $additionalSets; // زيادة 30%

        return $this->price + $additionalCost;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('ends_at', '>', now())
                    ->whereNull('cancelled_at');
    }

    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<', now());
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('ends_at', '<=', now()->addDays($days))
                    ->where('ends_at', '>', now());
    }

    public function scopeOnTrial($query)
    {
        return $query->where('status', 'trial')
                    ->where('trial_ends_at', '>', now());
    }

    // أنواع الخطط
    public static function getPlanTypes(): array
    {
        return [
            'basic' => 'أساسية',
            'standard' => 'قياسية',
            'premium' => 'مميزة',
            'enterprise' => 'للشركات'
        ];
    }

    // دورات الفوترة
    public static function getBillingCycles(): array
    {
        return [
            'monthly' => 'شهرية',
            'quarterly' => 'ربع سنوية',
            'yearly' => 'سنوية'
        ];
    }

    // حالات الاشتراك
    public static function getStatuses(): array
    {
        return [
            'trial' => 'تجريبي',
            'active' => 'نشط',
            'expired' => 'منتهي الصلاحية',
            'cancelled' => 'ملغي',
            'suspended' => 'معلق'
        ];
    }
}
