import React, { useState, useRef, useEffect } from 'react';
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  User,
  CreditCard,
  Banknote,
  Receipt,
  Scan,
  Calculator,
  Percent,
  Package,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import ApiService from '@/services/api';

// نوع بيانات المنتج
interface Product {
  id: number;
  name: string;
  name_en?: string;
  sku: string;
  barcode?: string;
  selling_price: number;
  cost_price: number;
  stock_quantity: number;
  category?: {
    id: number;
    name: string;
  };
  image?: string;
  is_active: boolean;
}

// نوع بيانات عنصر السلة
interface CartItem {
  product: Product;
  quantity: number;
  discount: number;
}

const POS: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { toast } = useToast();
  const searchRef = useRef<HTMLInputElement>(null);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [customerName, setCustomerName] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(15); // ضريبة القيمة المضافة 15%

  // حالات البيانات
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // جلب المنتجات من API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        const response = await ApiService.getProducts({ is_active: true });
        if (response.success && response.data) {
          setProducts(response.data.data);
        }
      } catch (error) {
        console.error('خطأ في جلب المنتجات:', error);
        toast({
          title: "خطأ",
          description: "فشل في جلب المنتجات",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [toast]);

  // الفئات
  const categories = [
    { id: 'all', name: 'جميع المنتجات', nameEn: 'All Products' },
    { id: 'electronics', name: 'إلكترونيات', nameEn: 'Electronics' },
    { id: 'food', name: 'أطعمة ومشروبات', nameEn: 'Food & Beverages' },
    { id: 'clothing', name: 'ملابس', nameEn: 'Clothing' },
    { id: 'beauty', name: 'تجميل وعناية', nameEn: 'Beauty & Care' },
    { id: 'books', name: 'كتب ومجلات', nameEn: 'Books & Magazines' }
  ];

  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.name_en && product.name_en.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (product.barcode && product.barcode.includes(searchTerm)) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' ||
      (product.category && product.category.id.toString() === selectedCategory);
    return matchesSearch && matchesCategory && product.is_active;
  });

  // إضافة منتج للسلة
  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      if (existingItem.quantity < product.stock_quantity) {
        setCart(cart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        ));
      } else {
        toast({
          title: "تحذير",
          description: "لا يمكن إضافة كمية أكثر من المتوفر في المخزون",
          variant: "destructive"
        });
      }
    } else {
      setCart([...cart, { product, quantity: 1, discount: 0 }]);
    }

    // تنظيف البحث وإعادة التركيز
    setSearchTerm('');
    searchRef.current?.focus();
  };

  // تحديث كمية المنتج في السلة
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    const product = products.find(p => p.id === productId);
    if (product && newQuantity > product.stock_quantity) {
      toast({
        title: "تحذير",
        description: "الكمية المطلوبة غير متوفرة في المخزون",
        variant: "destructive"
      });
      return;
    }

    setCart(cart.map(item =>
      item.product.id === productId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  // حذف منتج من السلة
  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.product.id !== productId));
  };

  // حساب المجاميع
  const subtotal = cart.reduce((sum, item) => {
    const itemTotal = item.product.price * item.quantity;
    const itemDiscount = (itemTotal * item.discount) / 100;
    return sum + itemTotal - itemDiscount;
  }, 0);

  const discountAmount = (subtotal * discount) / 100;
  const taxableAmount = subtotal - discountAmount;
  const taxAmount = (taxableAmount * tax) / 100;
  const total = taxableAmount + taxAmount;

  // معالجة الدفع
  const handlePayment = async () => {
    if (cart.length === 0) {
      toast({
        title: "خطأ",
        description: "السلة فارغة، يرجى إضافة منتجات أولاً",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsProcessing(true);

      // إعداد بيانات البيع
      const saleData = {
        customer_name: customerName || null,
        payment_method: paymentMethod,
        discount_amount: discountAmount,
        tax_amount: taxAmount,
        subtotal: subtotal,
        total_amount: total,
        items: cart.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          unit_price: item.product.selling_price,
          total_price: item.quantity * item.product.selling_price,
          discount_amount: item.discount
        }))
      };

      // إرسال البيع إلى API
      const response = await ApiService.createPOSSale(saleData);

      if (response.success) {
        toast({
          title: "تم الدفع بنجاح",
          description: `تم إتمام العملية بمبلغ ${total.toFixed(2)} ر.س`,
        });

        // تفريغ السلة
        setCart([]);
        setCustomerName('');
        setDiscount(0);
        searchRef.current?.focus();
      } else {
        throw new Error(response.message || 'فشل في إتمام العملية');
      }
    } catch (error: any) {
      console.error('خطأ في إتمام البيع:', error);
      toast({
        title: "خطأ في الدفع",
        description: error.message || "فشل في إتمام العملية",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // البحث بالباركود
  const handleBarcodeSearch = (barcode: string) => {
    const product = products.find(p => p.barcode === barcode);
    if (product) {
      addToCart(product);
    } else {
      toast({
        title: "منتج غير موجود",
        description: "لم يتم العثور على منتج بهذا الباركود",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="h-full flex gap-6">
      {/* الجانب الأيمن - المنتجات والبحث */}
      <div className="flex-1 space-y-6">
        {/* شريط البحث والفئات */}
        <Card className="card-elevated">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="font-arabic">{t('pos.title')}</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Scan className="w-4 h-4 mr-2" />
                  <span className="font-arabic">مسح باركود</span>
                </Button>
                <Button variant="outline" size="sm">
                  <Calculator className="w-4 h-4 mr-2" />
                  <span className="font-arabic">آلة حاسبة</span>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* شريط البحث */}
            <div className="relative">
              <Search className={cn(
                "absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground",
                isRTL ? "right-3" : "left-3"
              )} />
              <Input
                ref={searchRef}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t('pos.search')}
                className={cn(
                  "input-enhanced text-lg h-12",
                  isRTL ? "pr-12" : "pl-12"
                )}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && filteredProducts.length === 1) {
                    addToCart(filteredProducts[0]);
                  }
                }}
              />
            </div>

            {/* فئات المنتجات */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="font-arabic"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* شبكة المنتجات */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              className="card-interactive cursor-pointer"
              onClick={() => addToCart(product)}
            >
              <CardContent className="p-4">
                <div className="aspect-square bg-muted rounded-lg mb-3 flex items-center justify-center">
                  <Package className="w-8 h-8 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-sm font-arabic line-clamp-2">
                    {product.name}
                  </h3>
                  <div className="flex items-center justify-between">
                    <span className="font-bold text-primary font-english">
                      {product.selling_price} ر.س
                    </span>
                    <Badge
                      variant={product.stock_quantity > 10 ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {product.stock_quantity}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground font-arabic">
              لا توجد منتجات تطابق البحث
            </p>
          </div>
        )}
      </div>

      {/* الجانب الأيسر - سلة المشتريات والدفع */}
      <div className="w-96 space-y-6">
        {/* معلومات العميل */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="text-lg font-arabic flex items-center gap-2">
              <User className="w-5 h-5" />
              معلومات العميل
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder="اسم العميل (اختياري)"
              className="input-enhanced font-arabic"
            />
          </CardContent>
        </Card>

        {/* سلة المشتريات */}
        <Card className="card-elevated flex-1">
          <CardHeader>
            <CardTitle className="text-lg font-arabic flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                {t('pos.cart')}
              </div>
              <Badge variant="secondary" className="font-english">
                {cart.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {cart.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground font-arabic">
                  السلة فارغة
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {cart.map((item) => (
                  <div key={item.product.id} className="p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm font-arabic flex-1">
                        {item.product.name}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFromCart(item.product.id)}
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                          className="h-7 w-7 p-0"
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="w-8 text-center font-english">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                          className="h-7 w-7 p-0"
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-bold text-sm font-english">
                          {(item.product.price * item.quantity).toFixed(2)} ر.س
                        </p>
                        <p className="text-xs text-muted-foreground font-english">
                          {item.product.price} × {item.quantity}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* خصم إجمالي */}
            {cart.length > 0 && (
              <div className="space-y-3 pt-3 border-t">
                <div className="flex items-center gap-2">
                  <Percent className="w-4 h-4 text-muted-foreground" />
                  <Input
                    type="number"
                    value={discount}
                    onChange={(e) => setDiscount(Math.max(0, Math.min(100, Number(e.target.value))))}
                    placeholder="خصم %"
                    className="input-enhanced text-sm"
                    min="0"
                    max="100"
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* ملخص الفاتورة */}
        {cart.length > 0 && (
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle className="text-lg font-arabic">ملخص الفاتورة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="font-arabic">المجموع الفرعي:</span>
                <span className="font-english">{subtotal.toFixed(2)} ر.س</span>
              </div>
              
              {discount > 0 && (
                <div className="flex justify-between text-destructive">
                  <span className="font-arabic">الخصم ({discount}%):</span>
                  <span className="font-english">-{discountAmount.toFixed(2)} ر.س</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="font-arabic">الضريبة ({tax}%):</span>
                <span className="font-english">{taxAmount.toFixed(2)} ر.س</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between text-lg font-bold">
                <span className="font-arabic">{t('pos.total')}:</span>
                <span className="font-english">{total.toFixed(2)} ر.س</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* طرق الدفع والتأكيد */}
        {cart.length > 0 && (
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle className="text-lg font-arabic">{t('pos.payment')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* طرق الدفع */}
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={paymentMethod === 'cash' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('cash')}
                  className="font-arabic"
                >
                  <Banknote className="w-4 h-4 mr-2" />
                  نقدي
                </Button>
                <Button
                  variant={paymentMethod === 'card' ? 'default' : 'outline'}
                  onClick={() => setPaymentMethod('card')}
                  className="font-arabic"
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  بطاقة
                </Button>
              </div>

              {/* أزرار الإجراءات */}
              <div className="space-y-2">
                <Button
                  onClick={handlePayment}
                  disabled={isProcessing}
                  className="w-full btn-gradient-primary h-12 text-lg font-arabic"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    'تأكيد الدفع'
                  )}
                </Button>
                <Button
                  variant="outline"
                  className="w-full font-arabic"
                >
                  <Receipt className="w-4 h-4 mr-2" />
                  {t('pos.print')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default POS;