<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'slug',
        'description',
        'description_en',
        'permissions',
        'is_system',
        'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_system' => 'boolean',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_roles')
                    ->withPivot(['company_id', 'branch_id'])
                    ->withTimestamps();
    }

    public function userRoles(): HasMany
    {
        return $this->hasMany(UserRole::class);
    }

    // الدوال المساعدة
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions ?? []);
    }

    public function givePermission(string $permission): self
    {
        $permissions = $this->permissions ?? [];
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->permissions = $permissions;
            $this->save();
        }
        return $this;
    }

    public function revokePermission(string $permission): self
    {
        $permissions = $this->permissions ?? [];
        $this->permissions = array_values(array_diff($permissions, [$permission]));
        $this->save();
        return $this;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    // الأدوار الافتراضية
    public static function getDefaultRoles(): array
    {
        return [
            [
                'name' => 'مدير النظام',
                'name_en' => 'System Admin',
                'slug' => 'admin',
                'description' => 'مدير النظام الرئيسي مع صلاحيات كاملة',
                'description_en' => 'System administrator with full permissions',
                'permissions' => ['*'], // جميع الصلاحيات
                'is_system' => true,
                'is_active' => true
            ],
            [
                'name' => 'مالك الشركة',
                'name_en' => 'Company Owner',
                'slug' => 'owner',
                'description' => 'مالك الشركة مع صلاحيات إدارية كاملة للشركة',
                'description_en' => 'Company owner with full company management permissions',
                'permissions' => [
                    'view_dashboard', 'view_statistics',
                    'view_sales', 'create_sale', 'edit_sales', 'delete_sales', 'refund_sales',
                    'view_products', 'create_product', 'edit_products', 'delete_products',
                    'view_inventory', 'edit_inventory', 'inventory_count',
                    'view_customers', 'create_customer', 'edit_customers', 'delete_customers',
                    'view_suppliers', 'create_supplier', 'edit_suppliers', 'delete_suppliers',
                    'view_reports', 'create_report', 'export_reports',
                    'view_users', 'create_user', 'edit_users', 'delete_users',
                    'view_settings', 'edit_settings'
                ],
                'is_system' => true,
                'is_active' => true
            ],
            [
                'name' => 'مدير الفرع',
                'name_en' => 'Branch Manager',
                'slug' => 'manager',
                'description' => 'مدير الفرع مع صلاحيات إدارة العمليات اليومية',
                'description_en' => 'Branch manager with daily operations management permissions',
                'permissions' => [
                    'view_dashboard', 'view_statistics',
                    'view_sales', 'create_sale', 'edit_sales', 'refund_sales',
                    'view_products', 'create_product', 'edit_products',
                    'view_inventory', 'edit_inventory', 'inventory_count',
                    'view_customers', 'create_customer', 'edit_customers',
                    'view_suppliers',
                    'view_reports', 'export_reports',
                    'view_users'
                ],
                'is_system' => true,
                'is_active' => true
            ],
            [
                'name' => 'كاشير',
                'name_en' => 'Cashier',
                'slug' => 'cashier',
                'description' => 'موظف الكاشير مع صلاحيات البيع الأساسية',
                'description_en' => 'Cashier with basic sales permissions',
                'permissions' => [
                    'view_dashboard',
                    'view_sales', 'create_sale',
                    'view_products',
                    'view_customers', 'create_customer'
                ],
                'is_system' => true,
                'is_active' => true
            ]
        ];
    }
}
