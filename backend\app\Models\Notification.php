<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Notification extends Model
{
    protected $fillable = [
        'type',
        'notifiable_type',
        'notifiable_id',
        'title',
        'title_en',
        'message',
        'message_en',
        'data',
        'priority',
        'channel',
        'read_at',
        'sent_at',
        'company_id',
        'branch_id'
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'sent_at' => 'datetime'
    ];

    // العلاقات
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    // الدوال المساعدة
    public function markAsRead()
    {
        $this->update(['read_at' => now()]);
    }

    public function markAsUnread()
    {
        $this->update(['read_at' => null]);
    }

    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    public function isSent(): bool
    {
        return !is_null($this->sent_at);
    }

    public function markAsSent()
    {
        $this->update(['sent_at' => now()]);
    }

    // أنواع التنبيهات
    public static function getTypes(): array
    {
        return [
            'low_stock' => 'مخزون منخفض',
            'out_of_stock' => 'نفاد المخزون',
            'product_expiry' => 'انتهاء صلاحية المنتج',
            'payment_due' => 'استحقاق دفعة',
            'subscription_expiry' => 'انتهاء الاشتراك',
            'new_sale' => 'مبيعة جديدة',
            'sale_refund' => 'استرداد مبيعة',
            'user_login' => 'تسجيل دخول مستخدم',
            'system_error' => 'خطأ في النظام',
            'backup_completed' => 'اكتمال النسخة الاحتياطية',
            'backup_failed' => 'فشل النسخة الاحتياطية'
        ];
    }

    // مستويات الأولوية
    public static function getPriorities(): array
    {
        return [
            'low' => 'منخفضة',
            'normal' => 'عادية',
            'high' => 'عالية',
            'urgent' => 'عاجلة'
        ];
    }

    // قنوات الإرسال
    public static function getChannels(): array
    {
        return [
            'database' => 'قاعدة البيانات',
            'email' => 'البريد الإلكتروني',
            'sms' => 'رسائل نصية',
            'push' => 'إشعارات فورية'
        ];
    }

    // إنشاء تنبيه جديد
    public static function createNotification(
        $notifiable,
        string $type,
        string $title,
        string $message,
        array $data = [],
        string $priority = 'normal',
        string $channel = 'database',
        $companyId = null,
        $branchId = null
    ) {
        return static::create([
            'type' => $type,
            'notifiable_type' => get_class($notifiable),
            'notifiable_id' => $notifiable->id,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'priority' => $priority,
            'channel' => $channel,
            'company_id' => $companyId,
            'branch_id' => $branchId
        ]);
    }

    // إنشاء تنبيه مخزون منخفض
    public static function createLowStockAlert(Product $product): self
    {
        return self::createNotification(
            $product,
            'low_stock',
            'مخزون منخفض',
            "المنتج {$product->name} وصل إلى الحد الأدنى للمخزون",
            [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'current_stock' => $product->stock_quantity,
                'min_stock' => $product->min_stock_level
            ],
            'high',
            'database',
            $product->company_id
        );
    }

    // إنشاء تنبيه نفاد المخزون
    public static function createOutOfStockAlert(Product $product): self
    {
        return self::createNotification(
            $product,
            'out_of_stock',
            'نفاد المخزون',
            "المنتج {$product->name} نفد من المخزون",
            [
                'product_id' => $product->id,
                'product_name' => $product->name
            ],
            'urgent',
            'database',
            $product->company_id
        );
    }

    // إنشاء تنبيه مبيعة جديدة
    public static function createNewSaleAlert(Sale $sale): self
    {
        return self::createNotification(
            $sale,
            'new_sale',
            'مبيعة جديدة',
            "تمت مبيعة جديدة بقيمة {$sale->total_amount} ريال",
            [
                'sale_id' => $sale->id,
                'sale_number' => $sale->sale_number,
                'total_amount' => $sale->total_amount,
                'customer_name' => $sale->customer?->name
            ],
            'normal',
            'database',
            $sale->company_id
        );
    }

    // إحصائيات الإشعارات
    public static function getStats(?int $companyId = null): array
    {
        $query = static::query();

        if ($companyId) {
            $query->where('company_id', $companyId);
        }

        return [
            'total' => $query->count(),
            'unread' => $query->clone()->unread()->count(),
            'high_priority' => $query->clone()->byPriority('high')->count(),
            'urgent' => $query->clone()->byPriority('urgent')->count()
        ];
    }
}
