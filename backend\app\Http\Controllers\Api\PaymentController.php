<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Subscription;
use App\Services\TapPaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    private TapPaymentService $tapService;

    public function __construct(TapPaymentService $tapService)
    {
        $this->tapService = $tapService;
    }

    /**
     * إنشاء دفعة جديدة
     */
    public function createPayment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payable_type' => 'required|string|in:subscription,invoice',
            'payable_id' => 'required|integer',
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|string|in:card,bank_transfer,wallet',
            'gateway' => 'required|string|in:tap,manual',
            'customer' => 'required|array',
            'customer.first_name' => 'required|string|max:50',
            'customer.last_name' => 'required|string|max:50',
            'customer.email' => 'required|email',
            'customer.phone.number' => 'required|string|max:15',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // إنشاء سجل الدفعة
            $payment = Payment::create([
                'payable_type' => $request->payable_type === 'subscription' ? Subscription::class : null,
                'payable_id' => $request->payable_id,
                'company_id' => auth()->user()->company_id,
                'user_id' => auth()->id(),
                'payment_method' => $request->payment_method,
                'gateway' => $request->gateway,
                'amount' => $request->amount,
                'currency' => 'SAR',
                'status' => 'pending',
                'metadata' => [
                    'customer' => $request->customer,
                    'created_via' => 'api',
                    'ip_address' => $request->ip()
                ]
            ]);

            if ($request->gateway === 'tap') {
                // إنشاء دفعة عبر Tap
                $tapResponse = $this->tapService->createCharge([
                    'amount' => $request->amount,
                    'currency' => 'SAR',
                    'description' => 'دفع اشتراك Wisaq POS',
                    'customer' => $request->customer,
                    'company_id' => auth()->user()->company_id,
                    'order_reference' => 'PAY-' . $payment->id,
                    'post_url' => config('app.url') . '/api/v1/payments/tap/webhook',
                    'redirect_url' => config('app.url') . '/payment/success'
                ]);

                if ($tapResponse['success']) {
                    $payment->update([
                        'gateway_transaction_id' => $tapResponse['data']['id'],
                        'gateway_reference' => $tapResponse['data']['reference']['transaction'] ?? null,
                        'gateway_response' => $tapResponse['data']
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'تم إنشاء الدفعة بنجاح',
                        'data' => [
                            'payment_id' => $payment->id,
                            'payment_url' => $tapResponse['payment_url'],
                            'amount' => $payment->amount,
                            'currency' => $payment->currency
                        ]
                    ]);
                } else {
                    $payment->markAsFailed($tapResponse['error'], $tapResponse);
                    
                    return response()->json([
                        'success' => false,
                        'message' => 'فشل في إنشاء الدفعة',
                        'error' => $tapResponse['error']
                    ], 400);
                }
            }

            // للدفع اليدوي
            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الدفعة بنجاح',
                'data' => [
                    'payment_id' => $payment->id,
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'status' => $payment->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'company_id' => auth()->user()->company_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الدفعة'
            ], 500);
        }
    }

    /**
     * معالجة webhook من Tap
     */
    public function handleTapWebhook(Request $request): JsonResponse
    {
        try {
            $signature = $request->header('X-Tap-Signature');
            $payload = $request->all();

            // التحقق من صحة webhook
            if (!$this->tapService->verifyWebhook($payload, $signature)) {
                Log::warning('Invalid Tap webhook signature', [
                    'payload' => $payload,
                    'signature' => $signature
                ]);

                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // معالجة webhook
            $result = $this->tapService->handleWebhook($payload);

            if ($result['success']) {
                $this->processWebhookEvent($payload);
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('Tap webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * معالجة أحداث webhook
     */
    private function processWebhookEvent(array $payload): void
    {
        $eventType = $payload['event'] ?? '';
        $chargeData = $payload['data'] ?? [];
        $chargeId = $chargeData['id'] ?? null;

        if (!$chargeId) {
            return;
        }

        $payment = Payment::where('gateway_transaction_id', $chargeId)->first();
        if (!$payment) {
            Log::warning('Payment not found for Tap charge', ['charge_id' => $chargeId]);
            return;
        }

        switch ($eventType) {
            case 'charge.updated':
                $status = $chargeData['status'] ?? '';
                
                if ($status === 'CAPTURED') {
                    $payment->markAsCompleted($chargeData);
                    $this->handleSuccessfulPayment($payment);
                } elseif (in_array($status, ['DECLINED', 'FAILED', 'CANCELLED'])) {
                    $payment->markAsFailed($chargeData['response']['message'] ?? 'Payment failed', $chargeData);
                }
                break;

            case 'charge.failed':
                $payment->markAsFailed($chargeData['response']['message'] ?? 'Payment failed', $chargeData);
                break;
        }
    }

    /**
     * معالجة الدفعة الناجحة
     */
    private function handleSuccessfulPayment(Payment $payment): void
    {
        if ($payment->payable_type === Subscription::class) {
            $subscription = $payment->payable;
            if ($subscription) {
                $subscription->markAsPaid();
                $subscription->extendSubscription();
            }
        }

        Log::info('Payment completed successfully', [
            'payment_id' => $payment->id,
            'amount' => $payment->amount,
            'company_id' => $payment->company_id
        ]);
    }

    /**
     * عرض تفاصيل دفعة
     */
    public function show($id): JsonResponse
    {
        $payment = Payment::with(['payable', 'company', 'user'])
                         ->where('company_id', auth()->user()->company_id)
                         ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $payment
        ]);
    }

    /**
     * قائمة المدفوعات
     */
    public function index(Request $request): JsonResponse
    {
        $query = Payment::with(['payable', 'company', 'user'])
                       ->where('company_id', auth()->user()->company_id);

        // فلترة حسب الحالة
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب البوابة
        if ($request->has('gateway')) {
            $query->where('gateway', $request->gateway);
        }

        // فلترة حسب التاريخ
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $query->orderBy('created_at', 'desc');

        $perPage = $request->get('per_page', 15);
        $payments = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    /**
     * الحصول على إعدادات Tap
     */
    public function getTapConfig(): JsonResponse
    {
        $config = $this->tapService->validateConfiguration();
        
        return response()->json([
            'success' => true,
            'data' => [
                'public_key' => $this->tapService->getPublicKey(),
                'environment' => $config['environment'],
                'is_configured' => $config['is_valid']
            ]
        ]);
    }
}
