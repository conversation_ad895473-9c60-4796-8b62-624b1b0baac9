<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Payment extends Model
{
    protected $fillable = [
        'payable_type',
        'payable_id',
        'company_id',
        'user_id',
        'payment_method',
        'gateway',
        'gateway_transaction_id',
        'gateway_reference',
        'amount',
        'currency',
        'status',
        'paid_at',
        'failed_at',
        'failure_reason',
        'gateway_response',
        'metadata',
        'notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'failed_at' => 'datetime',
        'gateway_response' => 'array',
        'metadata' => 'array'
    ];

    // العلاقات
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // الدوال المساعدة
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    public function markAsCompleted(array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'completed',
            'paid_at' => now(),
            'gateway_response' => $gatewayResponse,
            'failed_at' => null,
            'failure_reason' => null
        ]);
    }

    public function markAsFailed(string $reason, array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'failure_reason' => $reason,
            'gateway_response' => $gatewayResponse,
            'paid_at' => null
        ]);
    }

    public function markAsRefunded(array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'refunded',
            'gateway_response' => array_merge($this->gateway_response ?? [], $gatewayResponse)
        ]);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeByGateway($query, $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    // أنواع الحالات
    public static function getStatuses(): array
    {
        return [
            'pending' => 'في الانتظار',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتملة',
            'failed' => 'فاشلة',
            'cancelled' => 'ملغية',
            'refunded' => 'مستردة',
            'partially_refunded' => 'مستردة جزئياً'
        ];
    }

    // طرق الدفع
    public static function getPaymentMethods(): array
    {
        return [
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'wallet' => 'محفظة إلكترونية',
            'cash' => 'نقداً',
            'check' => 'شيك'
        ];
    }

    // البوابات المدعومة
    public static function getGateways(): array
    {
        return [
            'tap' => 'Tap Payments',
            'paypal' => 'PayPal',
            'stripe' => 'Stripe',
            'manual' => 'يدوي'
        ];
    }
}
