<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // إضافة middleware الأمان للجميع
        $middleware->append(\App\Http\Middleware\SecurityHeaders::class);

        // إضافة middleware تسجيل النشاط للـ API
        $middleware->appendToGroup('api', \App\Http\Middleware\ActivityLogger::class);

        // إضافة middleware rate limiting والصلاحيات
        $middleware->alias([
            'api_rate_limit' => \App\Http\Middleware\ApiRateLimit::class,
            'permission' => \App\Http\Middleware\CheckPermission::class,
            'role' => \App\Http\Middleware\CheckRole::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
