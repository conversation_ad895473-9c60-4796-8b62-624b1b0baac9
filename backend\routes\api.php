<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\SaleController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\SupplierController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\InventoryController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// المسارات العامة (بدون مصادقة)
Route::prefix('v1')->group(function () {
    // مسارات المصادقة مع Rate Limiting
    Route::post('/login', [AuthController::class, 'login'])->middleware('api_rate_limit:5,1');
    Route::post('/register', [AuthController::class, 'register'])->middleware('api_rate_limit:3,5');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->middleware('api_rate_limit:3,5');
    
    // مسارات محمية بالمصادقة
    Route::middleware('auth:sanctum')->group(function () {
        // مسارات المصادقة المحمية
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::put('/password', [AuthController::class, 'updatePassword']);
        
        // لوحة التحكم
        Route::get('/dashboard/stats', [DashboardController::class, 'getStats']);
        Route::get('/dashboard/recent-sales', [DashboardController::class, 'getRecentSales']);
        Route::get('/dashboard/top-products', [DashboardController::class, 'getTopProducts']);
        Route::get('/dashboard/sales-chart', [DashboardController::class, 'getSalesChart']);
        
        // إدارة الشركات (للمشرفين فقط)
        Route::middleware('role:admin,supervisor')->group(function () {
            Route::apiResource('companies', CompanyController::class);
        });
        
        // إدارة المنتجات
        Route::middleware('permission:view_products')->group(function () {
            Route::get('/products', [ProductController::class, 'index']);
            Route::get('/products/{product}', [ProductController::class, 'show']);
            Route::get('/products/search/{term}', [ProductController::class, 'search']);
            Route::get('/products/barcode/{barcode}', [ProductController::class, 'findByBarcode']);
            Route::get('/products/low-stock', [ProductController::class, 'getLowStock']);
        });

        Route::middleware('permission:create_product')->group(function () {
            Route::post('/products', [ProductController::class, 'store']);
            Route::post('/products/bulk-import', [ProductController::class, 'bulkImport']);
        });

        Route::middleware('permission:edit_products')->group(function () {
            Route::put('/products/{product}', [ProductController::class, 'update']);
            Route::patch('/products/{product}', [ProductController::class, 'update']);
        });

        Route::middleware('permission:delete_products')->group(function () {
            Route::delete('/products/{product}', [ProductController::class, 'destroy']);
        });
        
        // إدارة المبيعات
        Route::middleware('permission:view_sales')->group(function () {
            Route::get('/sales', [SaleController::class, 'index']);
            Route::get('/sales/{sale}', [SaleController::class, 'show']);
            Route::get('/sales/{sale}/receipt', [SaleController::class, 'getReceipt']);
        });

        Route::middleware('permission:create_sale')->group(function () {
            Route::post('/sales', [SaleController::class, 'store']);
            Route::post('/sales/pos', [SaleController::class, 'createPOSSale']);
        });

        Route::middleware('permission:edit_sales')->group(function () {
            Route::put('/sales/{sale}', [SaleController::class, 'update']);
            Route::patch('/sales/{sale}', [SaleController::class, 'update']);
        });

        Route::middleware('permission:delete_sales')->group(function () {
            Route::delete('/sales/{sale}', [SaleController::class, 'destroy']);
        });

        Route::middleware('permission:refund_sales')->group(function () {
            Route::post('/sales/{sale}/refund', [SaleController::class, 'refund']);
        });

        // إدارة العملاء
        Route::apiResource('customers', CustomerController::class);
        Route::get('/customers/search/{term}', [CustomerController::class, 'search']);
        Route::post('/customers/export', [CustomerController::class, 'export']);
        Route::post('/customers/import', [CustomerController::class, 'import']);

        // إدارة الموردين
        Route::apiResource('suppliers', SupplierController::class);
        Route::get('/suppliers/search/{term}', [SupplierController::class, 'search']);
        Route::get('/suppliers/preferred', [SupplierController::class, 'preferred']);

        // إدارة الفئات
        Route::apiResource('categories', CategoryController::class);
        Route::get('/categories/search/{term}', [CategoryController::class, 'search']);
        Route::post('/categories/reorder', [CategoryController::class, 'reorder']);
        Route::get('/categories/{category}/products', [CategoryController::class, 'products']);
        Route::post('/categories/{category}/move-products', [CategoryController::class, 'moveProducts']);

        // إدارة المخزون
        Route::get('/inventory', [InventoryController::class, 'index']);
        Route::post('/inventory/adjust-stock', [InventoryController::class, 'adjustStock']);
        Route::post('/inventory/bulk-adjust-stock', [InventoryController::class, 'bulkAdjustStock']);
        Route::get('/inventory/logs', [InventoryController::class, 'logs']);
        Route::get('/inventory/low-stock', [InventoryController::class, 'lowStock']);
        Route::get('/inventory/out-of-stock', [InventoryController::class, 'outOfStock']);
        Route::get('/inventory/stock-value', [InventoryController::class, 'stockValue']);
        
        // التقارير
        Route::prefix('reports')->group(function () {
            Route::get('/sales', [SaleController::class, 'getSalesReport']);
            Route::get('/products', [ProductController::class, 'getProductsReport']);
            Route::get('/inventory', [ProductController::class, 'getInventoryReport']);
        });

        // إدارة المدفوعات
        Route::get('/payments', [PaymentController::class, 'index']);
        Route::get('/payments/{payment}', [PaymentController::class, 'show']);
        Route::post('/payments/create', [PaymentController::class, 'createPayment']);
        Route::get('/payments/tap/config', [PaymentController::class, 'getTapConfig']);
    });

    // مسارات المدفوعات العامة (بدون مصادقة)
    Route::post('/payments/tap/webhook', [PaymentController::class, 'handleTapWebhook']);
});
