// إعداد API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = localStorage.getItem('auth_token');

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);

      if (response.status === 401) {
        // إزالة token منتهي الصلاحية
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        window.location.href = '/login';
        throw new Error('Unauthorized');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private async get(endpoint: string, params?: any): Promise<any> {
    const url = new URL(`${this.baseURL}${endpoint}`);
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });
    }

    return this.request(url.pathname + url.search);
  }

  private async post(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  private async put(endpoint: string, data?: any): Promise<any> {
    return this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  private async delete(endpoint: string): Promise<any> {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }

  // دوال المصادقة
  async login(credentials: { email: string; password: string }) {
    return this.post('/login', credentials);
  }

  async logout() {
    return this.post('/logout');
  }

  async getMe() {
    return this.get('/me');
  }

  // دوال لوحة التحكم
  async getDashboardStats() {
    return this.get('/dashboard/stats');
  }

  async getRecentSales() {
    return this.get('/dashboard/recent-sales');
  }

  async getLowStockProducts() {
    return this.get('/dashboard/low-stock');
  }

  // دوال المنتجات
  async getProducts(params?: any) {
    return this.get('/products', params);
  }

  async getProduct(id: number) {
    return this.get(`/products/${id}`);
  }

  async createProduct(data: any) {
    return this.post('/products', data);
  }

  async updateProduct(id: number, data: any) {
    return this.put(`/products/${id}`, data);
  }

  async deleteProduct(id: number) {
    return this.delete(`/products/${id}`);
  }

  async searchProducts(term: string) {
    return this.get(`/products/search/${term}`);
  }

  // دوال الفئات
  async getCategories(params?: any) {
    return this.get('/categories', params);
  }

  async createCategory(data: any) {
    return this.post('/categories', data);
  }

  async updateCategory(id: number, data: any) {
    return this.put(`/categories/${id}`, data);
  }

  async deleteCategory(id: number) {
    return this.delete(`/categories/${id}`);
  }

  // دوال المبيعات
  async getSales(params?: any) {
    return this.get('/sales', params);
  }

  async createSale(data: any) {
    return this.post('/sales', data);
  }

  async createPOSSale(data: any) {
    return this.post('/sales/pos', data);
  }

  async getSale(id: number) {
    return this.get(`/sales/${id}`);
  }

  async refundSale(id: number, data: any) {
    return this.post(`/sales/${id}/refund`, data);
  }

  async getSaleReceipt(id: number) {
    return this.get(`/sales/${id}/receipt`);
  }

  // دوال العملاء
  async getCustomers(params?: any) {
    return this.get('/customers', params);
  }

  async createCustomer(data: any) {
    return this.post('/customers', data);
  }

  async updateCustomer(id: number, data: any) {
    return this.put(`/customers/${id}`, data);
  }

  async deleteCustomer(id: number) {
    return this.delete(`/customers/${id}`);
  }

  async searchCustomers(term: string) {
    return this.get(`/customers/search/${term}`);
  }

  // دوال الموردين
  async getSuppliers(params?: any) {
    return this.get('/suppliers', params);
  }

  async createSupplier(data: any) {
    return this.post('/suppliers', data);
  }

  async updateSupplier(id: number, data: any) {
    return this.put(`/suppliers/${id}`, data);
  }

  async deleteSupplier(id: number) {
    return this.delete(`/suppliers/${id}`);
  }

  // دوال المخزون
  async getInventory(params?: any) {
    return this.get('/inventory', params);
  }

  async adjustStock(data: any) {
    return this.post('/inventory/adjust-stock', data);
  }

  async bulkAdjustStock(data: any) {
    return this.post('/inventory/bulk-adjust-stock', data);
  }

  async getInventoryLogs(params?: any) {
    return this.get('/inventory/logs', params);
  }

  async getLowStockItems() {
    return this.get('/inventory/low-stock');
  }

  async getOutOfStockItems() {
    return this.get('/inventory/out-of-stock');
  }

  async getStockValue() {
    return this.get('/inventory/stock-value');
  }

  // دوال المدفوعات
  async getPayments(params?: any) {
    return this.get('/payments', params);
  }

  async createPayment(data: any) {
    return this.post('/payments/create', data);
  }

  async getPayment(id: number) {
    return this.get(`/payments/${id}`);
  }

  async getTapConfig() {
    return this.get('/payments/tap/config');
  }

  // دوال التقارير
  async getSalesReport(params?: any) {
    return this.get('/reports/sales', params);
  }

  async getProductsReport(params?: any) {
    return this.get('/reports/products', params);
  }

  async getInventoryReport(params?: any) {
    return this.get('/reports/inventory', params);
  }

  // دوال عامة
  async uploadFile(file: File, path: string = 'general') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('path', path);

    const token = localStorage.getItem('auth_token');

    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Upload failed');
    }

    return response.json();
  }
}

export default new ApiService();
